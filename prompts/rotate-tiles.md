When rendering tiles (specifically fence, gate and road) consider the tiles next to them.
In the case of fence and gate, if there is a fence tile above or below the current tile, rotate the new tile asset 90 degrees to line up with it.
Do the same for roads, but only if a road tile is to the left or right.

Add two new tile settings for this

rotation_snap: none|horizontal|vertical
rotation_snap_tile: {tile to snap to (eg fence, road)}

Ensure this logic works both in the editor and in the game map.