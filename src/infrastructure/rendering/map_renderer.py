"""
Shared Map Rendering Component

This module provides a reusable map renderer that can be used by both the game
and the map editor to avoid code duplication.
"""

import pygame
from typing import List, Dict, Any, Optional, Tuple, Callable, Union
from dataclasses import dataclass

from src.application.interfaces import IAssetManager, LevelLayoutData, GameStateData
from src.infrastructure.logging import get_logger


@dataclass
class RenderConfig:
    """Configuration for map rendering."""
    tile_size: int
    show_grid: bool = False
    grid_color: Tuple[int, int, int] = (100, 100, 100)
    fallback_tile_color: Tuple[int, int, int] = (60, 70, 85)
    fallback_border_color: Tuple[int, int, int] = (80, 90, 105)
    fallback_entity_color: Tuple[int, int, int] = (255, 100, 100)
    error_colors: Dict[str, Tuple[int, int, int]] = None
    
    def __post_init__(self):
        """Initialize default error colors if not provided."""
        if self.error_colors is None:
            self.error_colors = {
                'generation_error': (255, 0, 0),      # Red for generation errors
                'missing_asset': (255, 255, 0),       # Yellow for missing assets
                'unexpected_error': (255, 0, 255),    # Magenta for unexpected errors
            }


@dataclass
class ViewportInfo:
    """Information about the current viewport."""
    camera_x: float
    camera_y: float
    screen_width: int
    screen_height: int
    world_to_screen_func: Callable[[Tuple[float, float]], Tuple[int, int]]


class MapRenderer:
    """
    Shared map rendering component for both game and editor.
    
    This class handles the common logic for rendering tiles and entities,
    with configurable parameters to support different use cases.
    """
    
    def __init__(self, asset_manager: IAssetManager):
        """
        Initialize the map renderer.
        
        Args:
            asset_manager: Asset manager for loading tile and entity assets
        """
        self.asset_manager = asset_manager
        self.logger = get_logger(__name__)
    
    def render_tiles(self, 
                    screen: pygame.Surface,
                    tiles: List[List[str]],
                    viewport: ViewportInfo,
                    config: RenderConfig,
                    tile_states: Optional[Dict[str, Dict[str, Any]]] = None) -> int:
        """
        Render map tiles to the screen.
        
        Args:
            screen: Pygame surface to render to
            tiles: 2D array of tile asset IDs
            viewport: Viewport information for camera and coordinate conversion
            config: Rendering configuration
            tile_states: Optional tile states for dynamic tiles (game only)
            
        Returns:
            Number of tiles rendered
        """
        if not tiles or not tiles[0]:
            return 0
        
        map_width = len(tiles[0])
        map_height = len(tiles)
        
        # Calculate visible tile range with safety margins
        start_x, end_x, start_y, end_y = self._calculate_visible_range(
            viewport, config.tile_size, map_width, map_height
        )
        
        tiles_rendered = 0
        
        # Render visible tiles
        for y in range(start_y, end_y):
            for x in range(start_x, end_x):
                if (0 <= y < map_height and 0 <= x < map_width):
                    asset_id = tiles[y][x]
                    
                    # Handle dynamic tile states (for game rendering)
                    if tile_states:
                        asset_id = self._apply_tile_state(asset_id, x, y, tile_states)
                    
                    if asset_id:  # Only render if there's an asset ID
                        screen_pos = viewport.world_to_screen_func((x * config.tile_size, y * config.tile_size))
                        
                        # Only render if the tile is actually visible on screen
                        if self._is_position_visible(screen_pos, config.tile_size, viewport):
                            if self._render_single_tile(screen, asset_id, screen_pos, config):
                                tiles_rendered += 1
        
        return tiles_rendered
    
    def render_entities(self,
                       screen: pygame.Surface,
                       entities: List[Dict[str, Any]],
                       viewport: ViewportInfo,
                       config: RenderConfig) -> int:
        """
        Render entities to the screen.
        
        Args:
            screen: Pygame surface to render to
            entities: List of entity definitions
            viewport: Viewport information for camera and coordinate conversion
            config: Rendering configuration
            
        Returns:
            Number of entities rendered
        """
        entities_rendered = 0
        
        for entity in entities:
            # Handle different entity position formats
            if 'position' in entity:
                # Game format: {'position': {'x': x, 'y': y}}
                entity_x = entity['position']['x']
                entity_y = entity['position']['y']
            else:
                # Editor format: {'x': x, 'y': y} (in tile coordinates)
                entity_x = entity.get('x', 0) * config.tile_size
                entity_y = entity.get('y', 0) * config.tile_size
            
            screen_pos = viewport.world_to_screen_func((entity_x, entity_y))
            
            # Only render if visible
            if self._is_position_visible(screen_pos, config.tile_size, viewport):
                asset_id = entity.get('asset_id')
                if asset_id:
                    if self._render_single_entity(screen, asset_id, screen_pos, config):
                        entities_rendered += 1
        
        return entities_rendered
    
    def render_grid(self,
                   screen: pygame.Surface,
                   map_width: int,
                   map_height: int,
                   viewport: ViewportInfo,
                   config: RenderConfig) -> None:
        """
        Render grid overlay.
        
        Args:
            screen: Pygame surface to render to
            map_width: Width of the map in tiles
            map_height: Height of the map in tiles
            viewport: Viewport information for camera and coordinate conversion
            config: Rendering configuration
        """
        if not config.show_grid:
            return
        
        # Calculate visible range
        start_x, end_x, start_y, end_y = self._calculate_visible_range(
            viewport, config.tile_size, map_width, map_height
        )
        
        # Draw vertical lines
        for x in range(start_x, end_x + 1):
            world_x = x * config.tile_size
            screen_x, _ = viewport.world_to_screen_func((world_x, 0))
            if 0 <= screen_x <= viewport.screen_width:
                pygame.draw.line(screen, config.grid_color,
                               (screen_x, 0), (screen_x, viewport.screen_height))
        
        # Draw horizontal lines
        for y in range(start_y, end_y + 1):
            world_y = y * config.tile_size
            _, screen_y = viewport.world_to_screen_func((0, world_y))
            if 0 <= screen_y <= viewport.screen_height:
                pygame.draw.line(screen, config.grid_color,
                               (0, screen_y), (viewport.screen_width, screen_y))
    
    def _calculate_visible_range(self,
                                viewport: ViewportInfo,
                                tile_size: int,
                                map_width: int,
                                map_height: int) -> Tuple[int, int, int, int]:
        """Calculate the range of tiles that are visible in the viewport."""
        # Use integer camera positions for consistent calculations
        cam_x = int(viewport.camera_x)
        cam_y = int(viewport.camera_y)
        
        # Calculate visible tile bounds with safety margins
        start_x = max(0, (cam_x // tile_size) - 2)
        start_y = max(0, (cam_y // tile_size) - 2)
        end_x = min(map_width, start_x + (viewport.screen_width // tile_size) + 4)
        end_y = min(map_height, start_y + (viewport.screen_height // tile_size) + 4)
        
        return start_x, end_x, start_y, end_y
    
    def _apply_tile_state(self,
                         asset_id: str,
                         x: int,
                         y: int,
                         tile_states: Dict[str, Dict[str, Any]]) -> str:
        """Apply dynamic tile state changes (for game rendering)."""
        tile_key = f"{x},{y}"
        if tile_key in tile_states:
            tile_state = tile_states[tile_key]
            if tile_state.get('can_interact', False) and tile_state.get('is_open', False):
                # Handle door states
                if asset_id == "tile.door.wooden":
                    return "tile.door.wooden.open"
        return asset_id
    
    def _is_position_visible(self,
                           screen_pos: Tuple[int, int],
                           tile_size: int,
                           viewport: ViewportInfo) -> bool:
        """Check if a position is visible within the viewport."""
        screen_x, screen_y = screen_pos
        return (-tile_size <= screen_x <= viewport.screen_width and
                -tile_size <= screen_y <= viewport.screen_height)
    
    def _render_single_tile(self,
                           screen: pygame.Surface,
                           asset_id: str,
                           screen_pos: Tuple[int, int],
                           config: RenderConfig) -> bool:
        """
        Render a single tile with error handling.
        
        Returns:
            True if tile was successfully rendered, False otherwise
        """
        try:
            tile_surface = self.asset_manager.get_asset(asset_id, (config.tile_size, config.tile_size))
            screen.blit(tile_surface, screen_pos)
            return True
        except RuntimeError as e:
            self.logger.error(f"Failed to generate tile '{asset_id}': {e}")
            self._render_fallback_tile(screen, screen_pos, config, 'generation_error')
        except KeyError as e:
            self.logger.warning(f"Unregistered tile asset '{asset_id}': {e}")
            self._render_fallback_tile(screen, screen_pos, config, 'missing_asset')
        except Exception as e:
            self.logger.error(f"Unexpected error with tile '{asset_id}': {e}")
            self._render_fallback_tile(screen, screen_pos, config, 'unexpected_error')
        
        return False
    
    def _render_single_entity(self,
                             screen: pygame.Surface,
                             asset_id: str,
                             screen_pos: Tuple[int, int],
                             config: RenderConfig) -> bool:
        """
        Render a single entity with error handling.
        
        Returns:
            True if entity was successfully rendered, False otherwise
        """
        try:
            entity_surface = self.asset_manager.get_asset(asset_id, (config.tile_size, config.tile_size))
            screen.blit(entity_surface, screen_pos)
            return True
        except Exception:
            # Fallback: draw colored circle
            center = (screen_pos[0] + config.tile_size // 2, screen_pos[1] + config.tile_size // 2)
            pygame.draw.circle(screen, config.fallback_entity_color, center, config.tile_size // 4)
        
        return False
    
    def _render_fallback_tile(self,
                             screen: pygame.Surface,
                             screen_pos: Tuple[int, int],
                             config: RenderConfig,
                             error_type: str) -> None:
        """Render a fallback colored rectangle for failed tiles."""
        fallback_rect = pygame.Rect(screen_pos[0], screen_pos[1], config.tile_size, config.tile_size)
        
        if error_type in config.error_colors:
            color = config.error_colors[error_type]
        else:
            color = config.fallback_tile_color
        
        pygame.draw.rect(screen, color, fallback_rect)
        pygame.draw.rect(screen, config.fallback_border_color, fallback_rect, 1)
